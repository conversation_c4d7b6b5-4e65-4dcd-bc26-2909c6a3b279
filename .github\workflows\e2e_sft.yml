# # Tests layout

# Each folder under tests/ corresponds to a test category for a sub-namespace in verl. For instance:
# - `tests/trainer` for testing functionality related to `verl/trainer`
# - `tests/models` for testing functionality related to `verl/models`
# - ...

# There are a few folders with `special_` prefix, created for special purposes:
# - `special_distributed`: unit tests that must run with multiple GPUs
# - `special_e2e`: end-to-end tests with training/generation scripts
# - `special_npu`: tests for NPUs
# - `special_sanity`: a suite of quick sanity tests
# - `special_standalone`: a set of test that are designed to run in dedicated environments

# Accelerators for tests 
# - By default tests are run with GPU available, except for the ones under `special_npu`, and any test script whose name ends with `on_cpu.py`.
# - For test scripts with `on_cpu.py` name suffix would be tested on CPU resources in linux environment.

# # Workflow layout

# All CI tests are configured by yaml files in `.github/workflows/`. Here's an overview of all test configs:
# 1. A list of always triggered CPU sanity tests: `check-pr-title.yml`, `secrets_scan.yml`, `check-pr-title,yml`, `pre-commit.yml`, `doc.yml`
# 2. Some heavy multi-GPU unit tests, such as `model.yml`, `vllm.yml`, `sgl.yml`
# 3. End-to-end tests: `e2e_*.yml`
# 4. Unit tests
#   - `cpu_unit_tests.yml`, run pytest on all scripts with file name pattern `tests/**/test_*_on_cpu.py`
#   - `gpu_unit_tests.yml`, run pytest on all scripts with file without the `on_cpu.py` suffix.
#   - Since cpu/gpu unit tests by default runs all tests under `tests`, please make sure tests are manually excluded in them when
#     - new workflow yaml is added to `.github/workflows`
#     - new tests are added to workflow mentioned in 2.

name: e2e_sft

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Recipes
      - "!recipe/**"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # Entrypoints
      - ".github/workflows/e2e_sft.yml"
      - "examples/data_preprocess/gsm8k.py"
      - "tests/special_e2e/sft"
      - "verl/trainer/fsdp_sft_trainer.py"
      - "verl/trainer/config/sft_trainer.yaml"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

env:
  IMAGE: "verl-ci-cn-beijing.cr.volces.com/verlai/verl:base-verl0.4-cu124-cudnn9.8-torch2.6-fa2.7.4"
  DYNAMIC_RUNNER_ENDPOINT: "https://sd10g3clalm04ug7alq90.apigateway-cn-beijing.volceapi.com/runner"

jobs:
  setup:
      if: github.repository_owner == 'volcengine'
      runs-on: ubuntu-latest
      outputs:
        runner-label: ${{ steps.create-runner.outputs.runner-label }}
        mlp-task-id: ${{ steps.create-runner.outputs.mlp-task-id }}
      steps:
        - uses: actions/checkout@v4
        - id: create-runner
          uses: volcengine/vemlp-github-runner@v1 
          with:
            mode: "create"
            faas-url: "${{ env.DYNAMIC_RUNNER_ENDPOINT }}"
            mlp-image: "${{ env.IMAGE }}"
  e2e_sft:
    needs: setup
    runs-on: ["${{ needs.setup.outputs.runner-label || 'L20x8' }}"]
    timeout-minutes: 20 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install peft
          pip3 install --no-deps -e .[test,gpu]
      - name: Prepare gsm8k dataset
        run: |
          ray stop --force
          python3 examples/data_preprocess/gsm8k.py
      - name: Running GSM8K E2E training tests on 8 L20 GPUs with rmpad using function rm
        run: |
          ray stop --force
          bash tests/special_e2e/sft/run_sft.sh
      - name: Running GSM8K E2E training tests on 8 L20 GPUs w/o rmpad using function rm
        run: |
          ray stop --force
          RM_PAD=False bash tests/special_e2e/sft/run_sft.sh
      - name: Running GSM8K E2E training tests on 8 L20 GPUs with sequence parallism
        run: |
          ray stop --force
          SP_SIZE=2 bash tests/special_e2e/sft/run_sft.sh
      - name: Check loss difference between sequence parallel vs. default implementation
        run: |
          ray stop --force
          ENTRYPOINT="tests/special_e2e/sft/test_sp_loss_match.py" SP_SIZE=2 bash tests/special_e2e/sft/run_sft.sh
      - name: Running GSM8K E2E training tests on 8 L20 GPUs with sequence parallism and liger
        run: |
          ray stop --force
          SP_SIZE=2 LIGER=True bash tests/special_e2e/sft/run_sft.sh
      - name: Running GSM8K E2E training tests with LoRA
        run: |
          ray stop --force
          LORA_RANK=32 bash tests/special_e2e/sft/run_sft.sh
      - name: Run GSM8K E2E training and resume tests resuming from the checkpoint manager
        run: |
          ray stop --force
          LORA_RANK=32 RESUME_MODE=auto TOTAL_TRAIN_STEP=2 bash tests/special_e2e/sft/run_sft.sh
      # TODO: multiturn
  
  cleanup:
    runs-on: ubuntu-latest
    needs: [setup, e2e_sft]
    if: always()
    steps:
      - id: destroy-runner
        uses: volcengine/vemlp-github-runner@v1
        with:
          mode: "destroy"
          faas-url: "${{ env.DYNAMIC_RUNNER_ENDPOINT }}"
          mlp-task-id: "${{ needs.setup.outputs.mlp-task-id }}"
