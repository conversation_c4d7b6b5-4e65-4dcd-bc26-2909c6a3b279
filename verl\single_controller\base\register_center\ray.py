# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


import ray


@ray.remote
class WorkerGroupRegisterCenter:
    def __init__(self, rank_zero_info):
        self.rank_zero_info = rank_zero_info
        # rank -> node_id
        self.workers_info: dict[int, str] = {}

    def get_rank_zero_info(self):
        return self.rank_zero_info

    def set_worker_info(self, rank, node_id) -> None:
        self.workers_info[rank] = node_id

    def get_worker_info(self) -> dict[int, str]:
        return self.workers_info


def create_worker_group_register_center(name, info):
    return WorkerGroupRegisterCenter.options(name=name).remote(info)
