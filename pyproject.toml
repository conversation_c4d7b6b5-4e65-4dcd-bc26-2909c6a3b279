# -------------------------------
# build-system
# -------------------------------
[build-system]
requires = [
    "setuptools>=61.0",
    "wheel"
]
build-backend = "setuptools.build_meta"

# -------------------------------
# project (PEP 621 metadata)
# -------------------------------
[project]
name = "verl"
# We'll mark the version as "dynamic" because it's read from the file "verl/version/version" 
# (PEP 621 calls this "dynamic version"). 
# The actual version is specified in the [tool.setuptools.dynamic] section below.
dynamic = ["version", "dependencies", "optional-dependencies", "authors", "urls"]

description = "verl: Volcano Engine Reinforcement Learning for LLM"
license = {text = "Apache-2.0"}  # Changed from file to text format
readme = {file = "README.md", content-type = "text/markdown"}
requires-python = ">=3.10"

# -------------------------------
# tool.ruff - Linting configuration
# -------------------------------
[tool.ruff]
# Note: While the formatter will attempt to format lines such that they remain within the line-length,
# it isn't a hard upper bound, and formatted lines may exceed the line-length.
line-length = 120
exclude = ["tests/workers/rollout/test_sglang_async_rollout_sf_tools.py", "scripts/legacy_model_merger.py"]

[tool.ruff.lint]
isort = {known-first-party = ["verl"]}
# c.f. https://github.com/vllm-project/vllm/blob/ce8d6b75fc0586045df75ee1568a5b5f9957251b/pyproject.toml
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # isort
    "I",
    "G",
]
ignore = [
    # star imports
    "F405", "F403",
    # lambda expression assignment
    "E731",
    # Loop control variable not used within loop body
    "B007",
    # f-string format
    "UP032",
    # `.log()` statement uses f-string
    "G004",
    # X | None for type annotations
    "UP045",
    # deprecated import
    "UP035",
]

# -------------------------------
# tool.setuptools - Additional config
# -------------------------------
[tool.setuptools]
# True means `setuptools` will attempt to include all relevant files in package_data automatically.
# This corresponds to `include_package_data=True` in setup.py.
include-package-data = true

# We read the version from a file in 'verl/version/version'
[tool.setuptools.dynamic]
version = {file = "verl/version/version"}

# If you need to mimic `package_dir={'': '.'}`:
[tool.setuptools.package-dir]
"" = "."

# If you need to include specific non-Python data (like YAML files or version file):
# This is the rough equivalent of package_data={'': ['version/*'], 'verl': ['trainer/config/*.yaml']}
[tool.setuptools.package-data]
verl = [
  "version/*",
  "trainer/config/*.yaml",
  "trainer/config/*/*.yaml",
]
